# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a browser extension built with WXT framework and React. WXT is a framework for building browser extensions with TypeScript, providing a unified development experience for Chrome, Firefox, and other browsers.

## Development Commands

```bash
# Development server (Chrome by default)
pnpm run dev

# Development server for Firefox
pnpm run dev:firefox

# Build for production (Chrome by default)
pnpm run build

# Build for Firefox
pnpm run build:firefox

# Create distribution zip files
pnpm run zip
pnpm run zip:firefox

# TypeScript compilation check (no emit)
pnpm run compile

# Prepare WXT (run after npm install)
pnpm run postinstall

# Testing commands
pnpm run test          # Run tests in watch mode
pnpm run test:run      # Run tests once
pnpm run test:coverage # Generate coverage report
```

## Architecture

### Framework: WXT + React

- **WXT**: Browser extension framework handling manifest generation, builds, and cross-browser compatibility
- **React**: UI framework for popup and content scripts
- **TypeScript**: Type safety across the extension

### Extension Structure

- **entrypoints/**: Contains all extension entry points
  - `background.ts`: Background script (service worker)
  - `content.ts`: Content script (injected into web pages)
  - `popup/`: Popup UI components and entry point
- **public/**: Static assets including icons
- **assets/**: Build-time assets (React logo, etc.)

### Entry Points

- **Background Script**: `entrypoints/background.ts` - Service worker for extension logic
- **Content Script**: `entrypoints/content.ts` - Runs on matching pages (`*://*.google.com/*`)
- **Popup**: `entrypoints/popup/main.tsx` - Extension popup UI entry point

### Configuration

- **wxt.config.ts**: WXT framework configuration with React module
- **tsconfig.json**: Extends WXT's TypeScript configuration with React JSX support

## Development Notes

### WXT Framework

- WXT handles manifest.json generation automatically
- Supports both Manifest V2 and V3
- Provides hot reload during development
- Handles cross-browser compatibility

### Content Script Targeting

- Currently targets Google.com (`*://*.google.com/*`)
- Modify `matches` array in `entrypoints/content.ts` to change targeting

### TypeScript Setup

- Uses WXT's generated TypeScript configuration as base
- Configured for React JSX transformation
- 但是需要手动配置 tsconfig.json，否则会报错

### Path Aliases

Configured in `wxt.config.ts` for clean imports:
```typescript
'@/*': ['./src/*'],
'@components/*': ['./src/components/*'],
'@features/*': ['./src/features/*'],
'@services/*': ['./src/services/*'],
'@utils/*': ['./src/utils/*']
```

### Build Process

- `npm run compile` for type checking without building
- WXT handles bundling, optimization, and manifest generation
- Output goes to `.output/` directory (gitignored)

## Extension Development Workflow

1. Run `npm run dev` to start development server
2. Load the extension from `.output/chrome-mv3` in Chrome developer mode
3. Make changes to code - WXT provides hot reload
4. Test across browsers using browser-specific dev commands
5. Build production version with `npm run build`
6. Create distribution files with `npm run zip`

## Package Management Notes

- Use pnpm as the package manager for this project
- When running `pnpm run dev`, no restart is required for changes to take effect

## Testing Framework

### Vitest Configuration
- **Test Runner**: Vitest with happy-dom environment
- **Coverage**: V8 provider with HTML, JSON, and text reports
- **Mock Service Worker**: API mocking with MSW for external services
- **Testing Libraries**: React Testing Library, Jest DOM matchers, User Events

### Test Structure
- Unit tests in `__tests__/` directories alongside source files
- Mock data in `__mocks__/` directories
- Integration tests in `src/test/` directory
- Browser extension API mocks in test setup

## Core Architecture

### Service Architecture
- **Feature-based Structure**: `/features/dictionary`, `/features/settings`, `/features/translate`
- **Service Layer**: Storage, API, and business logic abstraction
- **UI Manager**: Centralized Shadow DOM management for content scripts
- **Content Script Architecture**: Multiple specialized managers (tooltip, slider, highlight)

### Shadow DOM Integration
- Components use Shadow DOM for CSS isolation from host pages
- Custom UI manager handles Shadow DOM creation and lifecycle
- CSS-in-JS and CSS modules for styling isolation

### Translation System Architecture
- **TranslateManager**: Core translation orchestration
- **Scanner**: DOM element selection and filtering
- **EnhancedInjector**: Smart translation injection with duplicate detection
- **Duplicate Detection**: Multi-layered approach across parent-child relationships

## Development API

### Local Development
- Vite proxy configured for local API at `localhost:3003`
- Mock services available for offline development
- Debug utilities throughout codebase for troubleshooting

## Testing and Development Methodology

- **测试驱动开发 (Test-Driven Development)**:
  - 新功能开发之前需要创建健全的测试脚本
  - 遵循测试 - 开发 - 测试 - 用户界面测试的开发流程

- 项目文件组织治结构 @docs/projectfile.md

## Steering Documents

Project-specific guidance documents for AI assistants:

- **[Product Guide](.claude/steering/product.md)** - Product purpose, core features, user value proposition, and business logic rules
- **[Technical Guide](.claude/steering/tech.md)** - Tech stack, build system, development commands, and architectural conventions  
- **[Structure Guide](.claude/steering/structure.md)** - Directory organization, file naming patterns, and component architecture

## AI Assistant Guidance

- **Browser Extension Development Reminders**:
  - 记住 这个是浏览器插件 不要给我脚本在console里面跑的指令

## Translation and Language Support

- **中文回复**: 确保在进行代码和文档交互时，能够流畅地使用中文进行沟通和技术讨论

## API Testing Notes

- 记住 如果需要API测试目前只能在background测试